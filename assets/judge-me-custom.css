/* Custom CSS for Judge.me reviews */

/* Override the existing styles for the reviews title to make it an h2 */
.jdgm-rev-widg__title,
h2.jdgm-rev-widg__title,
.jdgm-rev-widg__title.h2,
h2.jdgm-rev-widg__title.h2 {
  display: block !important;
  visibility: visible !important;
  font-family: var(--heading-font-family) !important;
  font-weight: var(--heading-font-weight) !important;
  font-size: var(--text-h2) !important;
  line-height: 1.1 !important;
  margin-bottom: 1.5rem !important;
  color: rgb(var(--text-color)) !important;
  text-transform: var(--heading-text-transform) !important;
}

/* Ensure proper desktop sizing with higher specificity */
@media screen and (min-width: 700px) {
  .jdgm-rev-widg .jdgm-rev-widg__title,
  .jdgm-rev-widg h2.jdgm-rev-widg__title,
  .jdgm-rev-widg .jdgm-rev-widg__title.h2,
  .jdgm-rev-widg h2.jdgm-rev-widg__title.h2,
  #judgeme_product_reviews .jdgm-rev-widg__title,
  #judgeme_product_reviews h2.jdgm-rev-widg__title,
  #judgeme_product_reviews .jdgm-rev-widg__title.h2,
  #judgeme_product_reviews h2.jdgm-rev-widg__title.h2,
  .shopify-section .jdgm-rev-widg__title,
  .shopify-section h2.jdgm-rev-widg__title,
  .shopify-section .jdgm-rev-widg__title.h2,
  .shopify-section h2.jdgm-rev-widg__title.h2,
  [id*="judgeme"] .jdgm-rev-widg__title,
  [id*="judgeme"] h2.jdgm-rev-widg__title,
  [id*="judgeme"] .jdgm-rev-widg__title.h2,
  [id*="judgeme"] h2.jdgm-rev-widg__title.h2 {
    font-size: var(--text-h2) !important;
    font-family: var(--heading-font-family) !important;
    font-weight: var(--heading-font-weight) !important;
    line-height: 1.1 !important;
    text-transform: var(--heading-text-transform) !important;
  }
}

/* Additional fallback for larger screens */
@media screen and (min-width: 1400px) {
  .jdgm-rev-widg .jdgm-rev-widg__title,
  .jdgm-rev-widg h2.jdgm-rev-widg__title,
  .jdgm-rev-widg .jdgm-rev-widg__title.h2,
  .jdgm-rev-widg h2.jdgm-rev-widg__title.h2,
  #judgeme_product_reviews .jdgm-rev-widg__title,
  #judgeme_product_reviews h2.jdgm-rev-widg__title,
  #judgeme_product_reviews .jdgm-rev-widg__title.h2,
  #judgeme_product_reviews h2.jdgm-rev-widg__title.h2,
  .shopify-section .jdgm-rev-widg__title,
  .shopify-section h2.jdgm-rev-widg__title,
  .shopify-section .jdgm-rev-widg__title.h2,
  .shopify-section h2.jdgm-rev-widg__title.h2,
  [id*="judgeme"] .jdgm-rev-widg__title,
  [id*="judgeme"] h2.jdgm-rev-widg__title,
  [id*="judgeme"] .jdgm-rev-widg__title.h2,
  [id*="judgeme"] h2.jdgm-rev-widg__title.h2 {
    font-size: var(--text-h2) !important;
  }
}

/* Make sure the title is properly displayed */
.jdgm-widget-actions-wrapper {
  margin-top: 1.5rem !important;
}

/* Ensure proper spacing around the reviews widget */
.jdgm-rev-widg {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
  padding: 0 !important;
}

span.jdgm-prev-badge__text {
  font-family: 'FormaDJRText';
}
.jdgm-rev-widg__summary-inner {
  font-family: 'FormaDJRText';
}

.jdgm-rev-widg__sort-wrapper {
  font-family: 'FormaDJRText';
}

/* Desktop layout: Rating and button to the left of heading */
@media screen and (min-width: 700px) {
  /* Main header container - use flexbox for side-by-side layout */
  .jdgm-rev-widg__header {
    display: flex !important;
    align-items: flex-start !important;
    gap: 1rem !important; /* Reduced gap from 2rem to 1rem */
    margin-bottom: 2rem !important;
  }

  /* Left side: Rating and button container */
  .jdgm-row-stars {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 1rem !important;
    flex-shrink: 0 !important;
    width: auto !important;
    min-width: 180px !important; /* Reduced from 200px to 180px */
    max-width: 220px !important; /* Added max-width for better control */
  }

  /* Right side: Title takes remaining space */
  .jdgm-rev-widg__title,
  h2.jdgm-rev-widg__title,
  .jdgm-rev-widg__title.h2,
  h2.jdgm-rev-widg__title.h2 {
    flex: 1 !important;
    margin-bottom: 0 !important;
    width: auto !important;
  }

  /* Move the write review button to be with the rating */
  .jdgm-widget-actions-wrapper {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    order: 2 !important;
  }

  /* Summary section styling */
  .jdgm-rev-widg__summary {
    order: 1 !important;
    margin-bottom: 0 !important;
  }

  .jdgm-rev-widg__summary-inner {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.5rem !important;
  }

  /* Override existing theme2.css styles for desktop */
  html body .jdgm-rev-widg__header {
    text-align: left !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 1rem !important; /* Reduced gap from 2rem to 1rem */
    flex-direction: row !important;
  }

  html body h2.jdgm-rev-widg__title {
    width: auto !important;
    flex: 1 !important;
    margin-bottom: 0 !important;
  }

  html body .jdgm-row-stars {
    width: auto !important;
    min-width: 180px !important; /* Reduced from 200px to 180px */
    max-width: 220px !important; /* Added max-width for better control */
    flex-direction: column !important;
    flex-shrink: 0 !important;
  }

  html body .jdgm-widget-actions-wrapper {
    text-align: left !important;
    margin: 0 !important;
    width: auto !important;
  }

  html body .jdgm-rev-widg__summary {
    width: 100% !important;
    justify-content: flex-start !important;
    text-align: left !important;
    margin-left: 0 !important;
    margin-bottom: 0 !important;
  }

  /* Fine-tune the layout balance */
  .jdgm-rev-widg__header {
    padding-left: 0 !important;
    margin-left: 0 !important;
  }

  /* Ensure the rating summary is compact */
  .jdgm-rev-widg__summary-inner {
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Make the write review button more compact */
  .jdgm-widget-actions-wrapper {
    padding: 0 !important;
  }

  .jdgm-write-rev-link {
    margin: 0 !important;
    padding: 0.75rem 1.5rem !important; /* Slightly smaller padding */
  }
}

/* Mobile layout: Keep stacked layout */
@media screen and (max-width: 699px) {
  .jdgm-rev-widg__header {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 1rem !important;
  }

  .jdgm-row-stars {
    width: 100% !important;
    order: 2 !important;
  }

  .jdgm-rev-widg__title,
  h2.jdgm-rev-widg__title,
  .jdgm-rev-widg__title.h2,
  h2.jdgm-rev-widg__title.h2 {
    order: 1 !important;
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }

  .jdgm-widget-actions-wrapper {
    order: 3 !important;
    width: 100% !important;
  }

  /* Override theme2.css mobile styles */
  html body .jdgm-rev-widg__header {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  html body h2.jdgm-rev-widg__title {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }

  html body .jdgm-row-stars {
    width: 100% !important;
  }
}

/* Individual review titles - make them smaller */
.jdgm-rev__title {
  font-size: 1.25rem !important; /* Smaller than the default h6 size */
  font-family: var(--heading-font-family) !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin-bottom: 0.75rem !important;
  color: rgb(var(--text-color)) !important;
}

/* Override existing theme.css styling for individual review titles */
html body .jdgm-rev__title {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  visibility: visible !important;
}

/* Responsive sizing for individual review titles */
@media screen and (max-width: 699px) {
  .jdgm-rev__title,
  html body .jdgm-rev__title {
    font-size: 1.125rem !important; /* Even smaller on mobile */
  }
}